#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置集成测试脚本
验证各个模块是否正确应用配置参数
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

from innovation_model_investigation.utils.config import Config
from innovation_model_investigation.data.preprocessor import EnterpriseDataPreprocessor
from innovation_model_investigation.features.binning import OptimalBinning
from innovation_model_investigation.features.woe_encoder import WOEEncoder
from innovation_model_investigation.models.trainer import ModelTrainer


def test_config_initialization():
    """测试配置初始化"""
    print("🧪 测试1: 配置初始化")
    print("-" * 40)
    
    config = Config()
    
    # 验证配置是否正确加载
    assert config.get('data.target_column') == 'label'
    assert config.get('data.test_size') == 0.3
    assert config.get('binning.min_n_bins') == 3
    assert config.get('binning.max_n_bins') == 6
    assert config.get('model.cv_folds') == 5
    
    print("✅ 配置初始化测试通过")
    return config


def test_preprocessor_config(config):
    """测试数据预处理器配置应用"""
    print("\n🧪 测试2: 数据预处理器配置应用")
    print("-" * 40)
    
    # 测试带配置的初始化
    preprocessor_with_config = EnterpriseDataPreprocessor(config=config)
    assert preprocessor_with_config.preprocessing_config['numerical_strategy'] == 'median'
    assert preprocessor_with_config.preprocessing_config['categorical_strategy'] == 'mode'
    assert preprocessor_with_config.preprocessing_config['outlier_method'] == 'iqr'
    
    # 测试不带配置的初始化（向后兼容性）
    preprocessor_without_config = EnterpriseDataPreprocessor()
    assert preprocessor_without_config.preprocessing_config['numerical_strategy'] == 'median'
    
    print("✅ 数据预处理器配置应用测试通过")


def test_binning_config(config):
    """测试分箱器配置应用"""
    print("\n🧪 测试3: 分箱器配置应用")
    print("-" * 40)
    
    # 测试带配置的初始化
    binning_with_config = OptimalBinning(config=config)
    assert binning_with_config.min_n_bins == 3
    assert binning_with_config.max_n_bins == 6
    assert binning_with_config.min_bin_size == 0.05
    
    # 测试不带配置的初始化（向后兼容性）
    binning_without_config = OptimalBinning()
    assert binning_without_config.min_n_bins == 3
    
    # 测试参数覆盖
    binning_override = OptimalBinning(min_n_bins=5, config=config)
    assert binning_override.min_n_bins == 5  # 应该使用传入的参数而不是配置中的值
    assert binning_override.max_n_bins == 6  # 其他参数仍使用配置中的值
    
    print("✅ 分箱器配置应用测试通过")


def test_woe_encoder_config(config):
    """测试WOE编码器配置应用"""
    print("\n🧪 测试4: WOE编码器配置应用")
    print("-" * 40)
    
    # 测试带配置的初始化
    woe_with_config = WOEEncoder(config=config)
    assert woe_with_config.regularization == 1e-6
    assert woe_with_config.handle_unknown == 'error'
    
    # 测试不带配置的初始化（向后兼容性）
    woe_without_config = WOEEncoder()
    assert woe_without_config.regularization == 1e-6
    
    print("✅ WOE编码器配置应用测试通过")


def test_model_trainer_config(config):
    """测试模型训练器配置应用"""
    print("\n🧪 测试5: 模型训练器配置应用")
    print("-" * 40)
    
    # 测试带配置的初始化
    trainer_with_config = ModelTrainer(config=config)
    assert trainer_with_config.model_config['cv_folds'] == 5
    assert trainer_with_config.model_config['scoring'] == 'roc_auc'
    assert trainer_with_config.model_config['algorithm'] == 'logistic_regression'
    
    # 测试不带配置的初始化（向后兼容性）
    trainer_without_config = ModelTrainer()
    assert trainer_without_config.model_config['cv_folds'] == 5
    
    print("✅ 模型训练器配置应用测试通过")


def test_custom_config():
    """测试自定义配置"""
    print("\n🧪 测试6: 自定义配置")
    print("-" * 40)
    
    # 创建自定义配置
    custom_config_dict = {
        'binning': {
            'min_n_bins': 4,
            'max_n_bins': 8
        },
        'model': {
            'cv_folds': 10
        }
    }
    
    custom_config = Config(custom_config_dict)
    
    # 验证自定义配置是否生效
    assert custom_config.get('binning.min_n_bins') == 4
    assert custom_config.get('binning.max_n_bins') == 8
    assert custom_config.get('model.cv_folds') == 10
    
    # 验证未修改的配置是否保持默认值
    assert custom_config.get('data.test_size') == 0.3
    
    # 测试模块是否使用自定义配置
    binning_custom = OptimalBinning(config=custom_config)
    assert binning_custom.min_n_bins == 4
    assert binning_custom.max_n_bins == 8
    
    trainer_custom = ModelTrainer(config=custom_config)
    assert trainer_custom.model_config['cv_folds'] == 10
    
    print("✅ 自定义配置测试通过")


def main():
    """主测试函数"""
    print("🚀 开始配置集成测试")
    print("=" * 60)
    
    try:
        # 运行所有测试
        config = test_config_initialization()
        test_preprocessor_config(config)
        test_binning_config(config)
        test_woe_encoder_config(config)
        test_model_trainer_config(config)
        test_custom_config()
        
        print("\n" + "=" * 60)
        print("🎉 所有配置集成测试通过！")
        print("✅ 配置管理器已成功集成到所有模块")
        print("✅ 向后兼容性保持良好")
        print("✅ 自定义配置功能正常")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
