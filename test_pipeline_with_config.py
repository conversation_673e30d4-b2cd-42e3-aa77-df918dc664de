#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置集成的Pipeline测试脚本
验证整个流程是否能正常工作
"""

import sys
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
sys.path.append(str(Path(__file__).parent / "src"))

from innovation_model_investigation.utils.config import Config
from innovation_model_investigation.data.loader import EnterpriseDataLoader
from innovation_model_investigation.data.preprocessor import EnterpriseDataPreprocessor
from innovation_model_investigation.features.binning import OptimalBinning
from innovation_model_investigation.features.woe_encoder import WOEEncoder
from innovation_model_investigation.models.trainer import ModelTrainer
from sklearn.model_selection import train_test_split


def test_pipeline_with_config():
    """测试带配置的完整pipeline"""
    print("🚀 测试带配置的完整Pipeline")
    print("=" * 60)
    
    # 1. 初始化配置
    print("\n⚙️ 步骤1: 配置初始化")
    config = Config()
    print(f"✅ 配置初始化完成")
    print(f"   - 目标列: {config.get('data.target_column')}")
    print(f"   - 测试集比例: {config.get('data.test_size')}")
    print(f"   - 分箱数范围: {config.get('binning.min_n_bins')}-{config.get('binning.max_n_bins')}")
    
    # 2. 数据加载
    print("\n📥 步骤2: 数据加载")
    try:
        loader = EnterpriseDataLoader()
        data_path = config.get('data.sample_data_path', 'data/enterprise_risk_sample_data.csv')
        dict_path = config.get('data.data_dict_path', 'data/data_dictionary.csv')
        
        if not Path(data_path).exists():
            print(f"⚠️ 数据文件不存在: {data_path}")
            print("   创建模拟数据进行测试...")
            # 创建模拟数据
            np.random.seed(42)
            n_samples = 1000
            n_features = 10
            
            # 生成特征数据
            features_data = {}
            for i in range(n_features):
                features_data[f'feature_{i}'] = np.random.normal(0, 1, n_samples)
            
            # 生成目标变量
            features_data['label'] = np.random.binomial(1, 0.3, n_samples)
            
            data = pd.DataFrame(features_data)
            features = data.drop(columns=['label'])
            target = data['label']
        else:
            data = loader.load_data(data_path, dict_path)
            features, target = loader.get_features_and_target()
        
        print(f"✅ 数据加载完成: {len(features)} 样本, {len(features.columns)} 特征")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 3. 数据分割
    print("\n✂️ 步骤3: 数据分割")
    test_size = config.get('data.test_size', 0.3)
    random_state = config.get('data.random_state', 42)
    
    X_train, X_test, y_train, y_test = train_test_split(
        features, target,
        test_size=test_size,
        random_state=random_state,
        stratify=target
    )
    print(f"✅ 数据分割完成: 训练集{len(X_train)}, 测试集{len(X_test)}")
    
    # 4. 数据预处理
    print("\n🔧 步骤4: 数据预处理")
    preprocessor = EnterpriseDataPreprocessor(config=config)
    
    # 合并训练数据进行预处理
    train_data = X_train.copy()
    train_data['label'] = y_train
    
    X_train_processed, y_train_processed = preprocessor.preprocess_data(train_data)
    print(f"✅ 数据预处理完成: {X_train_processed.shape}")
    
    # 5. 最优分箱
    print("\n🔄 步骤5: 最优分箱")
    binning = OptimalBinning(config=config)
    
    # 只对前5个特征进行分箱测试（避免时间过长）
    test_features = X_train_processed.iloc[:, :5]
    binning_results = binning.fit_transform_all(test_features, y_train_processed)
    
    print(f"✅ 分箱完成: {len(binning_results)} 个特征")
    
    # 6. WOE编码
    print("\n🔢 步骤6: WOE编码")
    woe_encoder = WOEEncoder(config=config)
    
    X_train_woe = woe_encoder.fit_transform(test_features, y_train_processed, binning_results)
    print(f"✅ WOE编码完成: {X_train_woe.shape}")
    
    # 7. 模型训练
    print("\n🎯 步骤7: 模型训练")
    trainer = ModelTrainer(config=config)
    
    # 简化的训练参数
    simple_param_grid = {
        'C': [0.1, 1.0],
        'penalty': ['l2'],
        'solver': ['liblinear'],
        'max_iter': [100]
    }
    
    try:
        training_results = trainer.train_logistic_regression(
            X_train_woe, y_train_processed,
            param_grid=simple_param_grid,
            cv_folds=3  # 减少CV折数以加快测试
        )
        print(f"✅ 模型训练完成")
        print(f"   - 最佳参数: {trainer.best_params}")
        print(f"   - CV平均分数: {trainer.cv_scores.mean():.4f}")
        
    except Exception as e:
        print(f"❌ 模型训练失败: {e}")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 Pipeline测试完成！")
    print("✅ 所有步骤都成功使用了配置参数")
    print("✅ 配置集成工作正常")
    
    return True


def test_custom_config_pipeline():
    """测试自定义配置的pipeline"""
    print("\n\n🚀 测试自定义配置的Pipeline")
    print("=" * 60)
    
    # 创建自定义配置
    custom_config_dict = {
        'data': {
            'test_size': 0.2,
            'random_state': 123
        },
        'binning': {
            'min_n_bins': 2,
            'max_n_bins': 4
        },
        'model': {
            'cv_folds': 3
        }
    }
    
    config = Config(custom_config_dict)
    
    print(f"✅ 自定义配置创建完成")
    print(f"   - 测试集比例: {config.get('data.test_size')}")
    print(f"   - 随机种子: {config.get('data.random_state')}")
    print(f"   - 分箱数范围: {config.get('binning.min_n_bins')}-{config.get('binning.max_n_bins')}")
    print(f"   - CV折数: {config.get('model.cv_folds')}")
    
    # 验证模块是否使用了自定义配置
    binning = OptimalBinning(config=config)
    trainer = ModelTrainer(config=config)
    
    assert binning.min_n_bins == 2
    assert binning.max_n_bins == 4
    assert trainer.model_config['cv_folds'] == 3
    
    print("✅ 自定义配置验证通过")
    
    return True


def main():
    """主测试函数"""
    try:
        # 测试默认配置的pipeline
        success1 = test_pipeline_with_config()
        
        # 测试自定义配置的pipeline
        success2 = test_custom_config_pipeline()
        
        if success1 and success2:
            print("\n" + "=" * 60)
            print("🎉 所有Pipeline测试通过！")
            print("✅ 配置管理器完美集成")
            print("✅ 支持自定义配置")
            print("✅ 向后兼容性良好")
        else:
            print("\n❌ 部分测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
