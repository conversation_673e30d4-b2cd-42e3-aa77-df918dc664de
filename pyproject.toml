[project]
name = "innovation-model-investigation"
version = "0.1.1"
description = "企业风险评估模型权重优化项目"
authors = [{name = "Your Name", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    # 数据处理
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    
    # 机器学习核心
    "scikit-learn>=1.3.0",
    "scipy>=1.11.0",
    
    # 评分卡专用库
    "optbinning>=0.17.0",
    "scorecardpy>=*******",
    
    # 可视化
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.15.0",
    
    # 模型解释
    "shap>=0.42.0",
    
    # 开发工具
    "jupyter>=1.0.0",
    "pytest>=7.0.0",
    
    # 数据验证
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "black",
    "isort",
    "flake8",
    "mypy",
]

[build-system]
requires = ["setuptools", "wheel"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
