# 基于 Baseline 的统一对比分析报告

**生成时间**: 2025-07-15 16:01:20
**分析版本**: 0.1.1, 0.1.0
**数据来源**: outputs/<version>/baseline/ 目录

## 📊 各版本模型 vs 传统方法对比

### 版本 0.1.1

- **模型AUC**: 0.9275
- **传统AUC**: 1.0000
- **提升幅度**: -7.2%
- **优胜者**: traditional

### 版本 0.1.0

- **模型AUC**: 0.9275
- **传统AUC**: 1.0000
- **提升幅度**: -7.2%
- **优胜者**: traditional

## 🔄 版本演进分析

**对比版本**: 0.1.0 → 0.1.1

| 指标 | 基线值 | 当前值 | 变化 | 变化率 | 状态 |
|------|--------|--------|------|--------|------|
| TEST_AUC | 0.9275 | 0.9275 | +0.0000 | +0.0% | 🔵 基本稳定 |
| TEST_KS | 0.8529 | 0.8529 | +0.0000 | +0.0% | 🔵 基本稳定 |
| TEST_ACCURACY | 0.7917 | 0.8125 | +0.0208 | +2.6% | 🟢 轻微提升 |
| TEST_PRECISION | 0.8333 | 0.8571 | +0.0238 | +2.9% | 🟢 轻微提升 |
| TEST_RECALL | 0.3571 | 0.4286 | +0.0714 | +20.0% | 🟢 显著提升 |
| TEST_F1 | 0.5000 | 0.5714 | +0.0714 | +14.3% | 🟢 显著提升 |

## 🔍 数据漂移分析

**漂移检测摘要**: 0.1.0 → 0.1.1

- 总特征数: 15
- 稳定特征: 15 🟢
- 轻微漂移: 0 🟡
- 严重漂移: 0 🔴

✅ 所有特征数据稳定，无漂移风险

## 💡 数据来源说明

本报告基于以下 baseline 数据生成：
- `baseline_performance.json`: 模型性能基准
- `comparison_results.json`: 详细对比分析
- `model_scorecard_compare.csv`: 样本级对比数据

这些数据由 `run_pipeline.py` 自动生成，整合了 evaluation/ 目录下的所有对比相关信息。

---
*报告由基于 baseline 的统一对比系统自动生成*
