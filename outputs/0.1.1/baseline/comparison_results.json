{"version": "0.1.1", "generated_time": "2025-07-15T15:58:30.426477", "overall_comparison": {"model_auc": 0.9275210084033614, "traditional_auc": 1.0, "improvement": -0.07247899159663862, "improvement_pct": -7.247899159663862, "winner": "traditional"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 159, "traditional_auc": 1.0, "model_auc": 0.939209726443769, "auc_improvement": -0.06079027355623101, "improvement_pct": -6.079027355623101}, "train": {"subset_name": "训练集", "sample_count": 111, "traditional_auc": 0.9999999999999999, "model_auc": 0.9461926961926962, "auc_improvement": -0.0538073038073037, "improvement_pct": -5.38073038073037}, "test": {"subset_name": "测试集", "sample_count": 48, "traditional_auc": 1.0, "model_auc": 0.9275210084033614, "auc_improvement": -0.07247899159663862, "improvement_pct": -7.247899159663862}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}