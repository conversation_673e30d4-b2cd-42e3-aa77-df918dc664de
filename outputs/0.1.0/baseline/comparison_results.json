{"version": "0.1.0", "generated_time": "2025-07-15T15:28:48.880663", "overall_comparison": {"model_auc": 0.9275210084033613, "traditional_auc": 1.0, "improvement": -0.07247899159663873, "improvement_pct": -7.2478991596638735, "winner": "traditional"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 159, "traditional_auc": 1.0, "model_auc": 0.9381648936170213, "auc_improvement": -0.06183510638297873, "improvement_pct": -6.183510638297873}, "train": {"subset_name": "训练集", "sample_count": 111, "traditional_auc": 0.9999999999999999, "model_auc": 0.9456099456099456, "auc_improvement": -0.05***************, "improvement_pct": -5.***************}, "test": {"subset_name": "测试集", "sample_count": 48, "traditional_auc": 1.0, "model_auc": 0.9275210084033613, "auc_improvement": -0.07247899159663873, "improvement_pct": -7.2478991596638735}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}