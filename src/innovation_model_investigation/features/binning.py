"""
最优分箱算法实现
基于IV值和卡方统计量的最优分箱
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
from optbinning import OptimalBinning as OptBin
import logging

logger = logging.getLogger(__name__)


class OptimalBinning:
    """最优分箱器"""
    
    def __init__(self, 
                 min_prebin_size: float = 0.05,
                 min_n_bins: int = 3, 
                 max_n_bins: int = 6,
                 min_bin_size: float = 0.05,
                 max_pvalue: float = 0.05,
                 gamma: float = 0.0):
        """
        初始化最优分箱器
        
        Args:
            min_prebin_size: 预分箱最小样本比例
            min_n_bins: 最少分箱数
            max_n_bins: 最多分箱数  
            min_bin_size: 每个分箱最小样本比例
            max_pvalue: 最大p值阈值
            gamma: 正则化参数
        """
        self.min_prebin_size = min_prebin_size
        self.min_n_bins = min_n_bins
        self.max_n_bins = max_n_bins
        self.min_bin_size = min_bin_size
        self.max_pvalue = max_pvalue
        self.gamma = gamma
        
        self.binning_results = {}
        
    def fit_transform_single(self,
                           feature: pd.Series,
                           target: pd.Series,
                           feature_name: str) -> Dict:
        """
        对单个特征进行最优分箱

        Args:
            feature: 特征序列
            target: 目标变量序列
            feature_name: 特征名称

        Returns:
            分箱结果字典
        """
        try:
            # 检查有效值数量
            valid_mask = ~(feature.isna() | feature.isnull())
            valid_count = valid_mask.sum()

            if valid_count == 0:
                logger.warning(f"特征 {feature_name} 完全没有有效值，跳过分箱")
                return self._create_empty_binning(feature_name)

            if valid_count < 10:  # 有效值太少
                logger.warning(f"特征 {feature_name} 有效值太少 ({valid_count})，使用简单分箱")
                return self._create_simple_binning(feature, target, feature_name)

            # 过滤有效数据
            feature_valid = feature[valid_mask]
            target_valid = target[valid_mask]

            # 检查特征值的唯一性
            unique_values = feature_valid.nunique()
            if unique_values <= 2:
                logger.warning(f"特征 {feature_name} 唯一值太少 ({unique_values})，使用简单分箱")
                return self._create_simple_binning(feature, target, feature_name)

            # 创建分箱器
            optb = OptBin(
                name=feature_name,
                dtype="numerical",
                min_prebin_size=self.min_prebin_size,
                min_n_bins=self.min_n_bins,
                max_n_bins=self.max_n_bins,
                min_bin_size=self.min_bin_size,
                max_pvalue=self.max_pvalue,
                gamma=self.gamma,
                verbose=False
            )

            # 拟合分箱
            optb.fit(feature_valid.values, target_valid.values)

            # 获取分箱信息
            binning_table = optb.binning_table

            result = {
                "feature_name": feature_name,
                "n_bins": len(binning_table.build()),
                "iv": optb.binning_table.iv,
                "splits": optb.splits,
                "binning_table": binning_table.build(),
                "optb_instance": optb
            }

            self.binning_results[feature_name] = result

            logger.info(f"特征 {feature_name} 分箱完成: {result['n_bins']} 个分箱, IV={result['iv']:.4f}")

            return result

        except Exception as e:
            logger.error(f"特征 {feature_name} 分箱失败: {e}")
            # 返回备用分箱结果
            return self._create_fallback_binning(feature, target, feature_name)
    
    def fit_transform_all(self, 
                         features: pd.DataFrame, 
                         target: pd.Series) -> Dict[str, Dict]:
        """
        对所有特征进行最优分箱
        
        Args:
            features: 特征DataFrame
            target: 目标变量序列
            
        Returns:
            所有特征的分箱结果字典
        """
        logger.info(f"开始对 {len(features.columns)} 个特征进行最优分箱")
        
        all_results = {}
        
        for feature_name in features.columns:
            feature = features[feature_name]
            result = self.fit_transform_single(feature, target, feature_name)
            all_results[feature_name] = result
            
        # 按IV值排序
        sorted_results = dict(
            sorted(all_results.items(), 
                  key=lambda x: x[1]['iv'], 
                  reverse=True)
        )
        
        self.binning_results = sorted_results
        
        logger.info("所有特征分箱完成")
        return sorted_results
    
    def transform_all(self, features: pd.DataFrame) -> pd.DataFrame:
        """
        使用已拟合的分箱器转换新数据

        Args:
            features: 待转换的特征DataFrame

        Returns:
            分箱后的特征DataFrame
        """
        if not self.binning_results:
            raise ValueError("请先执行分箱拟合")

        transformed_features = pd.DataFrame(index=features.index)

        for feature_name in features.columns:
            if feature_name in self.binning_results:
                binning_result = self.binning_results[feature_name]

                # 处理空分箱的情况
                if binning_result.get('is_empty', False):
                    # 对于完全没有有效值的特征，创建一个特殊的分箱索引
                    # 将null值映射到特殊的分箱索引-1，其他值保持不变
                    transformed_features[feature_name] = pd.Series(-1, index=features.index)
                    continue

                if 'optb_instance' in binning_result and binning_result['optb_instance'] is not None:
                    # 使用optbinning实例转换
                    optb = binning_result['optb_instance']
                    try:
                        # 创建一个临时序列用于转换
                        feature_temp = features[feature_name].copy()
                        # 获取null值的掩码
                        null_mask = feature_temp.isna()
                        
                        # 将null值临时替换为一个特殊值（不会影响分箱结果）
                        feature_temp = feature_temp.fillna(-999)
                        
                        # 对所有值进行转换
                        transformed_values = optb.transform(feature_temp.values, metric="indices")
                        transformed_values = pd.Series(transformed_values, index=feature_temp.index)
                        
                        # 将null值的分箱索引设置为-1（表示特殊分箱）
                        if null_mask.any():
                            transformed_values[null_mask] = -1
                            
                        transformed_features[feature_name] = transformed_values
                    except Exception as e:
                        logger.warning(f"特征 {feature_name} optbinning转换失败: {e}，使用手动分箱")
                        # 回退到手动分箱
                        splits = binning_result.get('splits', [])
                        if splits:
                            bins = [-np.inf] + splits + [np.inf]
                            # 获取null值的掩码
                            null_mask = features[feature_name].isna()
                            
                            # 创建一个临时特征，用于分箱
                            feature_temp = features[feature_name].copy()
                            
                            # 将null值临时替换为一个特殊值（不会影响分箱结果）
                            feature_temp = feature_temp.fillna(-999)
                            
                            # 对所有值进行分箱
                            transformed_values = pd.cut(feature_temp, 
                                                      bins=bins, 
                                                      include_lowest=True, 
                                                      labels=False)
                            transformed_values = pd.Series(transformed_values, index=features.index)
                            
                            # 将null值的分箱索引设置为-1（表示特殊分箱）
                            if null_mask.any():
                                transformed_values[null_mask] = -1
                                
                            transformed_features[feature_name] = transformed_values
                        else:
                            # 如果没有分割点，将null值映射到-1，其他值保持不变
                            transformed_values = pd.Series(0, index=features.index)
                            null_mask = features[feature_name].isna()
                            transformed_values[null_mask] = -1
                            transformed_features[feature_name] = transformed_values
                else:
                    # 使用分割点手动转换
                    splits = binning_result.get('splits', [])
                    if splits:
                        bins = [-np.inf] + splits + [np.inf]
                        feature_filled = features[feature_name].fillna(-999)
                        transformed_values = pd.cut(feature_filled, bins=bins, include_lowest=True, labels=False)
                        transformed_features[feature_name] = transformed_values
                    else:
                        # 如果没有分割点，用-999填充NaN
                        transformed_features[feature_name] = features[feature_name].fillna(-999)
            else:
                logger.warning(f"特征 {feature_name} 未找到分箱结果")
                transformed_features[feature_name] = features[feature_name].fillna(-999)

        return transformed_features
    
    def get_binning_results(self) -> Dict[str, Dict]:
        """获取分箱结果"""
        return self.binning_results
    
    def _create_fallback_binning(self,
                              feature: pd.Series,
                              target: pd.Series,
                              feature_name: str) -> Dict:
        """创建备用分箱结果（当自动分箱失败时）"""
        try:
            # 检查是否有有效值
            valid_feature = feature.dropna()
            if len(valid_feature) == 0:
                return self._create_empty_binning(feature_name)

            # 使用等频分箱作为备选
            n_bins = min(4, len(valid_feature.unique()))
            if n_bins <= 1:
                return self._create_simple_binning(feature, target, feature_name)

            bins = np.quantile(valid_feature, np.linspace(0, 1, n_bins + 1))
            bins = np.unique(bins)  # 去重

            if len(bins) <= 2:
                bins = [valid_feature.min(), valid_feature.max()]

            result = {
                "feature_name": feature_name,
                "n_bins": max(1, len(bins) - 1),
                "iv": 0.0,
                "splits": bins[1:-1].tolist() if len(bins) > 2 else [],
                "binning_table": None,
                "optb_instance": None,
                "is_fallback": True
            }

            logger.warning(f"特征 {feature_name} 使用备用分箱")
            return result

        except Exception as e:
            logger.error(f"创建备用分箱失败: {e}")
            return self._create_empty_binning(feature_name)

    def _create_empty_binning(self, feature_name: str) -> Dict:
        """为完全没有有效值的特征创建空分箱"""
        result = {
            "feature_name": feature_name,
            "n_bins": 1,
            "iv": 0.0,
            "splits": [],
            "binning_table": None,
            "optb_instance": None,
            "is_empty": True
        }

        logger.warning(f"特征 {feature_name} 没有有效值，创建空分箱")
        return result

    def _create_simple_binning(self,
                             feature: pd.Series,
                             target: pd.Series,
                             feature_name: str) -> Dict:
        """为有效值很少的特征创建简单分箱"""
        try:
            # 检查是否有NaN值
            has_nan = feature.isna().any()
            valid_feature = feature.dropna()
            
            if len(valid_feature) == 0:
                return self._create_empty_binning(feature_name)

            # 简单的二分箱：使用中位数分割
            median_val = valid_feature.median()
            
            # 即使只有一个唯一值，如果有NaN值，也创建一个分割点
            # 这样可以确保NaN值被视为一个单独的分箱
            if len(valid_feature.unique()) > 1:
                splits = [median_val]
            elif has_nan:
                # 如果只有一个唯一值但有NaN，创建一个特殊的分割点
                # 使用该唯一值作为分割点，这样非NaN值会在一个分箱，NaN值会在另一个分箱
                splits = [valid_feature.iloc[0] - 0.1]
            else:
                splits = []

            result = {
                "feature_name": feature_name,
                "n_bins": 2 if splits else 1,
                "iv": 0.1,  # 给一个小的IV值
                "splits": splits,
                "binning_table": None,
                "optb_instance": None,
                "is_simple": True
            }

            logger.warning(f"特征 {feature_name} 使用简单分箱")
            return result

        except Exception as e:
            logger.error(f"创建简单分箱失败: {e}")
            return self._create_empty_binning(feature_name)
    
    def get_iv_ranking(self) -> pd.DataFrame:
        """
        获取特征IV值排序
        
        Returns:
            包含特征名和IV值的排序DataFrame
        """
        if not self.binning_results:
            raise ValueError("请先执行分箱")
            
        iv_data = [
            {
                "feature_name": name,
                "iv": result["iv"],
                "n_bins": result["n_bins"]
            }
            for name, result in self.binning_results.items()
        ]
        
        df = pd.DataFrame(iv_data)
        return df.sort_values("iv", ascending=False).reset_index(drop=True)
    
    def plot_binning_summary(self):
        """绘制分箱汇总图表"""
        if not self.binning_results:
            raise ValueError("请先执行分箱")
            
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        iv_df = self.get_iv_ranking()
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # IV值分布
        ax1.barh(range(len(iv_df)), iv_df['iv'])
        ax1.set_yticks(range(len(iv_df)))
        ax1.set_yticklabels(iv_df['feature_name'], fontsize=8)
        ax1.set_xlabel('IV值')
        ax1.set_title('特征重要性排序 (IV值)')
        ax1.grid(axis='x', alpha=0.3)
        
        # 分箱数分布
        bin_counts = iv_df['n_bins'].value_counts().sort_index()
        ax2.bar(bin_counts.index, bin_counts.values)
        ax2.set_xlabel('分箱数')
        ax2.set_ylabel('特征数量')
        ax2.set_title('分箱数分布')
        ax2.grid(axis='y', alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
        return fig
        
    def save_binning_results(self, filepath: str):
        """保存分箱结果到文件"""
        import pickle
        
        with open(filepath, 'wb') as f:
            pickle.dump(self.binning_results, f)
            
        logger.info(f"分箱结果已保存到: {filepath}")
    
    def load_binning_results(self, filepath: str):
        """从文件加载分箱结果"""
        import pickle
        
        with open(filepath, 'rb') as f:
            self.binning_results = pickle.load(f)
            
        logger.info(f"分箱结果已加载自: {filepath}")