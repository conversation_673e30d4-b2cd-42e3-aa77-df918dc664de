#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将 metric_diligence_statistic_1.csv 转换为标准企业风险样本格式
- 字段名中文转英文code
- label 粗略打标（综合评分>=60为好企业0，否则1）
- 生成 innovation_health_level 字段
- 生成 data_generate_time 字段
- enterprise_id 用公司名
- 输出到 data/metric_diligence_statistic_1_labeled.csv
"""
import pandas as pd
import numpy as np
from datetime import datetime

# 1. 读取数据
raw_path = 'data/metric_diligence_statistic_1.csv'
dict_path = 'data/data_dictionary.csv'
out_path = 'data/enterprise_risk_sample_data.csv'

raw_df = pd.read_csv(raw_path)
dict_df = pd.read_csv(dict_path)

# 2. 构建中文名到英文code映射
dict_map = dict(zip(dict_df['indicator_name'], dict_df['indicator_code']))

# 3. 字段重命名（只处理有映射的列）
rename_cols = {col: dict_map[col] for col in raw_df.columns if col in dict_map}
converted_df = raw_df.rename(columns=rename_cols)

# 4. enterprise_id 字段（直接从原始 raw_df 取，避免重命名后丢失）
name_col = '企业名称'
if name_col not in raw_df.columns:
    raise ValueError('原始数据缺少“企业名称”列，请检查数据文件！')
converted_df['enterprise_id'] = raw_df[name_col]
converted_df['enterprise_name'] = raw_df[name_col]

# 处理不同类型的缺失值填充逻辑
# 1. 找出binary类型的列
binary_cols = dict_df.loc[dict_df['formula_type'] == 'binary', 'indicator_code'].tolist()

# 2. 针对binary列，缺失值填充为0
for col in binary_cols:
    if col in converted_df.columns:
        converted_df[col] = converted_df[col].fillna(0)
# 3. 针对 percentage 类型，检查范围并可选填充
percentage_cols = dict_df.loc[dict_df['formula_type'] == 'percentage', 'indicator_code'].tolist()
for col in percentage_cols:
    if col in converted_df.columns:
        # 检查是否有超出0-1范围的值
        if (converted_df[col].dropna() > 1).any():
            print(f"⚠️ 警告：{col} 存在超出0-1范围的值，请检查原始数据！")
        # 可选：对缺失值填充为0或其他策略
        # converted_df[col] = converted_df[col].fillna(0)

# 3. 可扩展：不同类型的处理逻辑
# 例如 future_type_map = {'binary': lambda s: s.fillna(0), 'numeric': lambda s: s.fillna(-999)}
# for t, func in future_type_map.items():
#     cols = dict_df.loc[dict_df['formula_type'] == t, 'indicator_code'].tolist()
#     for col in cols:
#         if col in converted_df.columns:
#             converted_df[col] = func(converted_df[col])

# 5. label 粗略打标
def calc_label(row):
    try:
        score = float(row.get('comprehensive_score', row.get('综合评分', np.nan)))
        if np.isnan(score):
            return 1
        return 0 if score >= 60 else 1
    except Exception:
        return 1

if 'comprehensive_score' in converted_df.columns:
    score_col = 'comprehensive_score'
elif '综合评分' in converted_df.columns:
    score_col = '综合评分'
else:
    score_col = None

converted_df['label'] = converted_df.apply(calc_label, axis=1)

# 6. innovation_health_level 生成
def calc_level(score):
    try:
        score = float(score)
        if score >= 80:
            return '优秀'
        elif score >= 65:
            return '良好'
        elif score >= 50:
            return '一般'
        elif score >= 35:
            return '较差'
        else:
            return '差'
    except Exception:
        return '差'

if score_col:
    converted_df['innovation_health_level'] = converted_df[score_col].apply(calc_level)
else:
    converted_df['innovation_health_level'] = '差'

# 7. data_generate_time
converted_df['data_generate_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# 8. 字段顺序对齐（参考 data_dictionary.csv 字段顺序）
# 读取标准字段顺序
indicator_codes = dict_df['indicator_code'].tolist()
# 新生成的字段，按业务需求顺序排列
extra_cols = ['enterprise_id', 'enterprise_name', 'label', 'innovation_health_level', 'data_generate_time']
# 合并为最终输出顺序
sample_cols = extra_cols + indicator_codes

# 保证所有需要的列都在，缺失的补空
for col in sample_cols:
    if col not in converted_df.columns:
        converted_df[col] = np.nan

# 只对指标列做缺失值填充
indicator_cols = [dict_map[name] for name in dict_map if dict_map[name] in converted_df.columns]
# 这里不填充 NaN，保留原始数据
# converted_df[indicator_cols] = converted_df[indicator_cols].fillna(-999)

# 按标准顺序输出
converted_df = converted_df[sample_cols]

# 9. 保存
converted_df.to_csv(out_path, index=False, encoding='utf-8-sig')
print(f'✅ 转换完成，已保存到 {out_path}') 